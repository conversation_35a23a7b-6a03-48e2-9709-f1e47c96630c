# Tilt Rotor Quadcopter for Gazebo Ionic

A tilt rotor quadcopter drone model for Gazebo Ionic simulation with ROS2 integration. This project provides a complete simulation environment for a VTOL (Vertical Take-Off and Landing) quadcopter with tilting rotors.

## Features

- **Realistic Model**: Rectangular body, cylindrical arms, tilting motor housings, and propellers
- **Tilt Rotor Mechanism**: 4 independently controllable tilt rotors with ±90° range
- **Custom Plugin**: C++ plugin for tilt rotor control and motor management
- **ROS2 Integration**: Full ROS2 compatibility with standard message types
- **Built-in Sensors**: IMU sensor for attitude feedback
- **Demo Controller**: Simple Python controller for testing and demonstration

## Model Components

### Physical Structure
- **Body**: Rectangular cube (0.6m × 0.3m × 0.15m)
- **Arms**: 4 cylindrical arms extending diagonally from the body
- **Motor Housings**: 4 cylindrical tilt rotor assemblies
- **Propellers**: 4 rectangular propeller blades
- **Total Mass**: ~2.5 kg

### Joints and Degrees of Freedom
- **Tilt Joints**: 4 revolute joints for rotor tilting (±90°)
- **Propeller Joints**: 4 revolute joints for propeller rotation
- **Fixed Joints**: Arms attached to body, IMU attached to body

## Installation

### Prerequisites
- ROS2 (Humble or later)
- Gazebo Ionic
- Required ROS2 packages:
  - `ros_gz_sim`
  - `ros_gz_bridge`
  - `robot_state_publisher`

### Build Instructions

1. Clone this repository into your ROS2 workspace:
```bash
cd ~/ros2_ws/src
git clone <repository_url> tilt_rotor
```

2. Install dependencies:
```bash
cd ~/ros2_ws
rosdep install --from-paths src --ignore-src -r -y
```

3. Build the package:
```bash
colcon build --packages-select tilt_rotor
```

4. Source the workspace:
```bash
source install/setup.bash
```

## Usage

### Launch the Simulation

To spawn the tilt rotor quadcopter in Gazebo:

```bash
ros2 launch tilt_rotor spawn_tilt_rotor.launch.py
```

Optional parameters:
- `world:=<world_file>` - Specify a different world file
- `x:=<x_pos>` - X position (default: 0.0)
- `y:=<y_pos>` - Y position (default: 0.0)
- `z:=<z_pos>` - Z position (default: 1.0)

### Control the Drone

#### Method 1: Direct Command Topics

**Tilt Control** (4 values for 4 rotors in radians):
```bash
ros2 topic pub /tilt_rotor_quadcopter/tilt_command std_msgs/msg/Float64MultiArray "{data: [0.0, 0.0, 0.0, 0.0]}"
```

**Motor Control** (4 values for 4 rotors in rad/s):
```bash
ros2 topic pub /tilt_rotor_quadcopter/motor_command std_msgs/msg/Float64MultiArray "{data: [100.0, 100.0, 100.0, 100.0]}"
```

#### Method 2: Twist Commands (Higher Level)

```bash
ros2 topic pub /tilt_rotor_quadcopter/cmd_vel geometry_msgs/msg/Twist "{linear: {x: 0.0, y: 0.0, z: 1.0}, angular: {x: 0.0, y: 0.0, z: 0.0}}"
```

#### Method 3: Demo Controller

Run the included demo controller:
```bash
ros2 run tilt_rotor simple_controller.py
```

## ROS2 Topics

### Published Topics
- `/tilt_rotor_quadcopter/imu/data` (sensor_msgs/Imu) - IMU sensor data
- `/tilt_rotor_quadcopter/joint_states` (sensor_msgs/JointState) - Joint positions and velocities
- `/tilt_rotor_quadcopter/pose` (geometry_msgs/PoseArray) - Model pose information

### Subscribed Topics
- `/tilt_rotor_quadcopter/cmd_vel` (geometry_msgs/Twist) - High-level velocity commands
- `/tilt_rotor_quadcopter/tilt_command` (std_msgs/Float64MultiArray) - Direct tilt angle commands
- `/tilt_rotor_quadcopter/motor_command` (std_msgs/Float64MultiArray) - Direct motor speed commands

## Configuration

Edit `config/tilt_rotor_params.yaml` to modify:
- Physical parameters (mass, dimensions, limits)
- Control gains and mixing parameters
- Sensor configurations
- Topic names

## Plugin Details

The custom `TiltRotorPlugin` provides:
- **Tilt Control**: Position control of tilt joints with configurable limits
- **Motor Control**: Velocity control of propeller joints
- **Command Mixing**: Conversion from Twist commands to individual motor/tilt commands
- **Safety Limits**: Automatic clamping of commands to safe ranges

### Plugin Parameters
- `max_tilt_angle`: Maximum tilt angle in radians (default: π/2)
- `max_motor_speed`: Maximum motor speed in rad/s (default: 1000)
- `ros/namespace`: ROS namespace for topics

## Development

### Adding New Features
1. Modify the SDF model in `models/tilt_rotor_quadcopter/model.sdf`
2. Update the plugin in `plugins/tilt_rotor_plugin/src/tilt_rotor_plugin.cpp`
3. Rebuild with `colcon build`

### Testing
- Use the demo controller to verify basic functionality
- Monitor joint states and IMU data for debugging
- Adjust control parameters in the configuration file

## Troubleshooting

### Common Issues
1. **Plugin not loading**: Check that the plugin library is built and in the library path
2. **Model not spawning**: Verify SDF syntax and file paths
3. **No response to commands**: Check topic names and message formats
4. **Unstable flight**: Adjust control gains in the configuration file

### Debug Commands
```bash
# Check available topics
ros2 topic list

# Monitor joint states
ros2 topic echo /tilt_rotor_quadcopter/joint_states

# Check IMU data
ros2 topic echo /tilt_rotor_quadcopter/imu/data
```

## License

Apache 2.0 License

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request
