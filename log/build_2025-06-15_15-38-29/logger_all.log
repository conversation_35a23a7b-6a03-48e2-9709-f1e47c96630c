[0.181s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'tilt_rotor', '--cmake-args', '-DCMAKE_BUILD_TYPE=Release']
[0.182s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=4, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['tilt_rotor'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=['-DCMAKE_BUILD_TYPE=Release'], cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x7d24b3e660e0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7d24b3e65c30>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7d24b3e65c30>>, mixin_verb=('build',))
[0.454s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.454s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.454s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.454s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.454s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.454s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.454s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/code/tilt_rotor'
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.477s] DEBUG:colcon.colcon_core.package_identification:Package '.' with type 'ros.ament_cmake' and name 'tilt_rotor'
[0.477s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.478s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.478s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.478s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.478s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.519s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.519s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.523s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 292 installed packages in /opt/ros/humble
[0.525s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.616s] Level 5:colcon.colcon_core.verb:set package 'tilt_rotor' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.616s] Level 5:colcon.colcon_core.verb:set package 'tilt_rotor' build argument 'cmake_target' from command line to 'None'
[0.616s] Level 5:colcon.colcon_core.verb:set package 'tilt_rotor' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.617s] Level 5:colcon.colcon_core.verb:set package 'tilt_rotor' build argument 'cmake_clean_cache' from command line to 'False'
[0.617s] Level 5:colcon.colcon_core.verb:set package 'tilt_rotor' build argument 'cmake_clean_first' from command line to 'False'
[0.617s] Level 5:colcon.colcon_core.verb:set package 'tilt_rotor' build argument 'cmake_force_configure' from command line to 'False'
[0.617s] Level 5:colcon.colcon_core.verb:set package 'tilt_rotor' build argument 'ament_cmake_args' from command line to 'None'
[0.617s] Level 5:colcon.colcon_core.verb:set package 'tilt_rotor' build argument 'catkin_cmake_args' from command line to 'None'
[0.617s] Level 5:colcon.colcon_core.verb:set package 'tilt_rotor' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.617s] DEBUG:colcon.colcon_core.verb:Building package 'tilt_rotor' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/code/tilt_rotor/build/tilt_rotor', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/code/tilt_rotor/install/tilt_rotor', 'merge_install': False, 'path': '/home/<USER>/code/tilt_rotor', 'symlink_install': False, 'test_result_base': None}
[0.617s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.620s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.621s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/code/tilt_rotor' with build type 'ament_cmake'
[0.624s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/code/tilt_rotor'
[0.630s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.631s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.632s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.657s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/tilt_rotor/build/tilt_rotor -- -j4 -l4
[0.777s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/tilt_rotor/build/tilt_rotor -- -j4 -l4
[0.798s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/tilt_rotor/build/tilt_rotor
[0.823s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(tilt_rotor)
[0.825s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/tilt_rotor/build/tilt_rotor
[0.833s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor' for CMake module files
[0.834s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor' for CMake config files
[0.835s] Level 1:colcon.colcon_core.shell:create_environment_hook('tilt_rotor', 'cmake_prefix_path')
[0.835s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/hook/cmake_prefix_path.ps1'
[0.837s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/hook/cmake_prefix_path.dsv'
[0.838s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/hook/cmake_prefix_path.sh'
[0.839s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/lib'
[0.840s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/bin'
[0.840s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/lib/pkgconfig/tilt_rotor.pc'
[0.841s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/lib/python3.10/site-packages'
[0.842s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/bin'
[0.844s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.ps1'
[0.847s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.dsv'
[0.849s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.sh'
[0.851s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.bash'
[0.853s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.zsh'
[0.855s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/colcon-core/packages/tilt_rotor)
[0.856s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(tilt_rotor)
[0.856s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor' for CMake module files
[0.858s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor' for CMake config files
[0.859s] Level 1:colcon.colcon_core.shell:create_environment_hook('tilt_rotor', 'cmake_prefix_path')
[0.861s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/hook/cmake_prefix_path.ps1'
[0.863s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/hook/cmake_prefix_path.dsv'
[0.865s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/hook/cmake_prefix_path.sh'
[0.867s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/lib'
[0.867s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/bin'
[0.867s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/lib/pkgconfig/tilt_rotor.pc'
[0.867s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/lib/python3.10/site-packages'
[0.868s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/bin'
[0.868s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.ps1'
[0.870s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.dsv'
[0.871s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.sh'
[0.872s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.bash'
[0.873s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.zsh'
[0.874s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/colcon-core/packages/tilt_rotor)
[0.874s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.875s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.875s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[0.875s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.890s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.890s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.891s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.921s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.922s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/tilt_rotor/install/local_setup.ps1'
[0.925s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/code/tilt_rotor/install/_local_setup_util_ps1.py'
[0.931s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/tilt_rotor/install/setup.ps1'
[0.933s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/tilt_rotor/install/local_setup.sh'
[0.934s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/code/tilt_rotor/install/_local_setup_util_sh.py'
[0.935s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/tilt_rotor/install/setup.sh'
[0.937s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/tilt_rotor/install/local_setup.bash'
[0.938s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/tilt_rotor/install/setup.bash'
[0.939s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/tilt_rotor/install/local_setup.zsh'
[0.940s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/tilt_rotor/install/setup.zsh'
