[0.034s] Invoking command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/tilt_rotor/build/tilt_rotor -- -j4 -l4
[0.154s] Invoked command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/tilt_rotor/build/tilt_rotor -- -j4 -l4
[0.175s] Invoking command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/tilt_rotor/build/tilt_rotor
[0.192s] -- Install configuration: "Release"
[0.192s] -- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/models
[0.193s] -- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/models/tilt_rotor_quadcopter
[0.193s] -- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/models/tilt_rotor_quadcopter/model.config
[0.193s] -- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/models/tilt_rotor_quadcopter/model.sdf
[0.194s] -- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/launch
[0.194s] -- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/launch/spawn_tilt_rotor.launch.py
[0.194s] -- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/config
[0.194s] -- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/config/tilt_rotor_params.yaml
[0.195s] -- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/lib/tilt_rotor/simple_controller.py
[0.195s] -- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/lib/tilt_rotor/test_commands.py
[0.195s] -- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/ament_index/resource_index/package_run_dependencies/tilt_rotor
[0.195s] -- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/ament_index/resource_index/parent_prefix_path/tilt_rotor
[0.195s] -- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/environment/ament_prefix_path.sh
[0.195s] -- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/environment/ament_prefix_path.dsv
[0.196s] -- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/environment/path.sh
[0.196s] -- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/environment/path.dsv
[0.196s] -- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/local_setup.bash
[0.196s] -- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/local_setup.sh
[0.196s] -- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/local_setup.zsh
[0.196s] -- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/local_setup.dsv
[0.198s] -- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.dsv
[0.198s] -- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/ament_index/resource_index/packages/tilt_rotor
[0.198s] -- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/cmake/tilt_rotorConfig.cmake
[0.198s] -- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/cmake/tilt_rotorConfig-version.cmake
[0.198s] -- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.xml
[0.202s] Invoked command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/tilt_rotor/build/tilt_rotor
