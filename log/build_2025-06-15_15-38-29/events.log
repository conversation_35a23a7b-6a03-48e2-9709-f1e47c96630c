[0.000000] (-) TimerEvent: {}
[0.000540] (tilt_rotor) JobQueued: {'identifier': 'tilt_rotor', 'dependencies': OrderedDict()}
[0.001193] (tilt_rotor) JobStarted: {'identifier': 'tilt_rotor'}
[0.027326] (tilt_rotor) JobProgress: {'identifier': 'tilt_rotor', 'progress': 'cmake'}
[0.030974] (tilt_rotor) JobProgress: {'identifier': 'tilt_rotor', 'progress': 'build'}
[0.034231] (tilt_rotor) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/code/tilt_rotor/build/tilt_rotor', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/code/tilt_rotor/build/tilt_rotor', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('USER', 'mahmood'), ('LC_TIME', 'lzh_TW'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '3'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>/code'), ('TERM_PROGRAM_VERSION', '1.100.2'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'lzh_TW'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('SYSTEMD_EXEC_PID', '4747'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mahmood'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'mahmood'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/home/<USER>/miniconda3/condabin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/mahmood-MacBookAir:@/tmp/.ICE-unix/1782,unix/mahmood-MacBookAir:/tmp/.ICE-unix/1782'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'lzh_TW'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/624a0ccb_369a_4914_9434_201d56e436b5'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'lzh_TW'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.1CKM72'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-4cc291407c.sock'), ('GNOME_TERMINAL_SERVICE', ':1.119'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniconda3/bin/python'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'lzh_TW'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'lzh_TW'), ('LC_IDENTIFICATION', 'lzh_TW'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/code/tilt_rotor/build/tilt_rotor'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CONDA_EXE', '/home/<USER>/miniconda3/bin/conda'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'lzh_TW'), ('LC_PAPER', 'lzh_TW'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.098150] (-) TimerEvent: {}
[0.152722] (tilt_rotor) CommandEnded: {'returncode': 0}
[0.156849] (tilt_rotor) JobProgress: {'identifier': 'tilt_rotor', 'progress': 'install'}
[0.176007] (tilt_rotor) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/code/tilt_rotor/build/tilt_rotor'], 'cwd': '/home/<USER>/code/tilt_rotor/build/tilt_rotor', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('USER', 'mahmood'), ('LC_TIME', 'lzh_TW'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '3'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>/code'), ('TERM_PROGRAM_VERSION', '1.100.2'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'lzh_TW'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('SYSTEMD_EXEC_PID', '4747'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mahmood'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'mahmood'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/home/<USER>/miniconda3/condabin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/mahmood-MacBookAir:@/tmp/.ICE-unix/1782,unix/mahmood-MacBookAir:/tmp/.ICE-unix/1782'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'lzh_TW'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/624a0ccb_369a_4914_9434_201d56e436b5'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'lzh_TW'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.1CKM72'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-4cc291407c.sock'), ('GNOME_TERMINAL_SERVICE', ':1.119'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniconda3/bin/python'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'lzh_TW'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'lzh_TW'), ('LC_IDENTIFICATION', 'lzh_TW'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/code/tilt_rotor/build/tilt_rotor'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CONDA_EXE', '/home/<USER>/miniconda3/bin/conda'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'lzh_TW'), ('LC_PAPER', 'lzh_TW'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.192988] (tilt_rotor) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.193842] (tilt_rotor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/models\n'}
[0.194370] (tilt_rotor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/models/tilt_rotor_quadcopter\n'}
[0.194811] (tilt_rotor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/models/tilt_rotor_quadcopter/model.config\n'}
[0.195117] (tilt_rotor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/models/tilt_rotor_quadcopter/model.sdf\n'}
[0.195306] (tilt_rotor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/launch\n'}
[0.195623] (tilt_rotor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/launch/spawn_tilt_rotor.launch.py\n'}
[0.195975] (tilt_rotor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/config\n'}
[0.196149] (tilt_rotor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/config/tilt_rotor_params.yaml\n'}
[0.196316] (tilt_rotor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/lib/tilt_rotor/simple_controller.py\n'}
[0.196473] (tilt_rotor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/lib/tilt_rotor/test_commands.py\n'}
[0.196647] (tilt_rotor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/ament_index/resource_index/package_run_dependencies/tilt_rotor\n'}
[0.196855] (tilt_rotor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/ament_index/resource_index/parent_prefix_path/tilt_rotor\n'}
[0.197000] (tilt_rotor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/environment/ament_prefix_path.sh\n'}
[0.197140] (tilt_rotor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/environment/ament_prefix_path.dsv\n'}
[0.197280] (tilt_rotor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/environment/path.sh\n'}
[0.197417] (tilt_rotor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/environment/path.dsv\n'}
[0.197554] (tilt_rotor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/local_setup.bash\n'}
[0.197722] (tilt_rotor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/local_setup.sh\n'}
[0.197867] (tilt_rotor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/local_setup.zsh\n'}
[0.198006] (tilt_rotor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/local_setup.dsv\n'}
[0.198141] (-) TimerEvent: {}
[0.198805] (tilt_rotor) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.dsv\n'}
[0.199542] (tilt_rotor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/ament_index/resource_index/packages/tilt_rotor\n'}
[0.199806] (tilt_rotor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/cmake/tilt_rotorConfig.cmake\n'}
[0.199984] (tilt_rotor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/cmake/tilt_rotorConfig-version.cmake\n'}
[0.200149] (tilt_rotor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.xml\n'}
[0.202170] (tilt_rotor) CommandEnded: {'returncode': 0}
[0.254291] (tilt_rotor) JobEnded: {'identifier': 'tilt_rotor', 'rc': 0}
[0.255171] (-) EventReactorShutdown: {}
