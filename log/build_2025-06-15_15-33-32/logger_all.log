[0.431s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'tilt_rotor', '--cmake-args', '-DCMAKE_BUILD_TYPE=Release']
[0.431s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=4, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['tilt_rotor'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=['-DCMAKE_BUILD_TYPE=Release'], cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x780030a4a0e0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x780030a49c30>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x780030a49c30>>, mixin_verb=('build',))
[1.362s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[1.362s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[1.362s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[1.362s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[1.362s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[1.362s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[1.362s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/code/tilt_rotor'
[1.362s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[1.363s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[1.363s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[1.363s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[1.363s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[1.363s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[1.363s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[1.363s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[1.363s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[1.418s] DEBUG:colcon.colcon_core.package_identification:Package '.' with type 'ros.ament_cmake' and name 'tilt_rotor'
[1.419s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[1.419s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[1.419s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[1.419s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[1.420s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
