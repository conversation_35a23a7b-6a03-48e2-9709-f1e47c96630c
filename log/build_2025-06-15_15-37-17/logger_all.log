[0.793s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'tilt_rotor', '--cmake-args', '-DCMAKE_BUILD_TYPE=Release']
[0.793s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=4, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['tilt_rotor'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=['-DCMAKE_BUILD_TYPE=Release'], cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x7c0db80560e0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7c0db8055c30>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7c0db8055c30>>, mixin_verb=('build',))
[1.888s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[1.888s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[1.888s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[1.888s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[1.888s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[1.889s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[1.895s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/code/tilt_rotor'
[1.895s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[1.896s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[1.896s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[1.896s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[1.896s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[1.896s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[1.897s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[1.897s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[1.897s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[1.994s] DEBUG:colcon.colcon_core.package_identification:Package '.' with type 'ros.ament_cmake' and name 'tilt_rotor'
[1.995s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[1.995s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[1.995s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[1.995s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[1.995s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[2.156s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[2.156s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[2.177s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 292 installed packages in /opt/ros/humble
[2.180s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[2.584s] Level 5:colcon.colcon_core.verb:set package 'tilt_rotor' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[2.584s] Level 5:colcon.colcon_core.verb:set package 'tilt_rotor' build argument 'cmake_target' from command line to 'None'
[2.584s] Level 5:colcon.colcon_core.verb:set package 'tilt_rotor' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[2.584s] Level 5:colcon.colcon_core.verb:set package 'tilt_rotor' build argument 'cmake_clean_cache' from command line to 'False'
[2.584s] Level 5:colcon.colcon_core.verb:set package 'tilt_rotor' build argument 'cmake_clean_first' from command line to 'False'
[2.584s] Level 5:colcon.colcon_core.verb:set package 'tilt_rotor' build argument 'cmake_force_configure' from command line to 'False'
[2.585s] Level 5:colcon.colcon_core.verb:set package 'tilt_rotor' build argument 'ament_cmake_args' from command line to 'None'
[2.585s] Level 5:colcon.colcon_core.verb:set package 'tilt_rotor' build argument 'catkin_cmake_args' from command line to 'None'
[2.586s] Level 5:colcon.colcon_core.verb:set package 'tilt_rotor' build argument 'catkin_skip_building_tests' from command line to 'False'
[2.586s] DEBUG:colcon.colcon_core.verb:Building package 'tilt_rotor' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/code/tilt_rotor/build/tilt_rotor', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/code/tilt_rotor/install/tilt_rotor', 'merge_install': False, 'path': '/home/<USER>/code/tilt_rotor', 'symlink_install': False, 'test_result_base': None}
[2.593s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[2.597s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[2.598s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/code/tilt_rotor' with build type 'ament_cmake'
[2.598s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/code/tilt_rotor'
[2.618s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[2.619s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[2.619s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[2.671s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/code/tilt_rotor -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/tilt_rotor/install/tilt_rotor
[7.151s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/code/tilt_rotor -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/tilt_rotor/install/tilt_rotor
[7.164s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/tilt_rotor/build/tilt_rotor -- -j4 -l4
[7.480s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/tilt_rotor/build/tilt_rotor -- -j4 -l4
[7.554s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/tilt_rotor/build/tilt_rotor
[7.629s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(tilt_rotor)
[7.650s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/tilt_rotor/build/tilt_rotor
[7.662s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor' for CMake module files
[7.665s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor' for CMake config files
[7.666s] Level 1:colcon.colcon_core.shell:create_environment_hook('tilt_rotor', 'cmake_prefix_path')
[7.666s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/hook/cmake_prefix_path.ps1'
[7.677s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/hook/cmake_prefix_path.dsv'
[7.680s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/hook/cmake_prefix_path.sh'
[7.690s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/lib'
[7.691s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/bin'
[7.691s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/lib/pkgconfig/tilt_rotor.pc'
[7.699s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/lib/python3.10/site-packages'
[7.702s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/bin'
[7.703s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.ps1'
[7.713s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.dsv'
[7.715s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.sh'
[7.725s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.bash'
[7.728s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.zsh'
[7.737s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/colcon-core/packages/tilt_rotor)
[7.739s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(tilt_rotor)
[7.741s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor' for CMake module files
[7.749s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor' for CMake config files
[7.750s] Level 1:colcon.colcon_core.shell:create_environment_hook('tilt_rotor', 'cmake_prefix_path')
[7.752s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/hook/cmake_prefix_path.ps1'
[7.753s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/hook/cmake_prefix_path.dsv'
[7.761s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/hook/cmake_prefix_path.sh'
[7.765s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/lib'
[7.772s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/bin'
[7.772s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/lib/pkgconfig/tilt_rotor.pc'
[7.773s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/lib/python3.10/site-packages'
[7.776s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/bin'
[7.778s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.ps1'
[7.786s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.dsv'
[7.788s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.sh'
[7.797s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.bash'
[7.798s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.zsh'
[7.800s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/colcon-core/packages/tilt_rotor)
[7.802s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[7.809s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[7.809s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[7.809s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[7.858s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[7.858s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[7.858s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[7.932s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[7.934s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/tilt_rotor/install/local_setup.ps1'
[7.937s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/code/tilt_rotor/install/_local_setup_util_ps1.py'
[7.947s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/tilt_rotor/install/setup.ps1'
[7.949s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/tilt_rotor/install/local_setup.sh'
[7.959s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/code/tilt_rotor/install/_local_setup_util_sh.py'
[7.969s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/tilt_rotor/install/setup.sh'
[7.971s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/tilt_rotor/install/local_setup.bash'
[7.973s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/tilt_rotor/install/setup.bash'
[7.984s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/tilt_rotor/install/local_setup.zsh'
[7.992s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/tilt_rotor/install/setup.zsh'
