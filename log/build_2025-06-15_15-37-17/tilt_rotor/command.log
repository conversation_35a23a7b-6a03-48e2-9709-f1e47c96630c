Invoking command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/code/tilt_rotor -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/tilt_rotor/install/tilt_rotor
Invoked command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/code/tilt_rotor -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/tilt_rotor/install/tilt_rotor
Invoking command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/tilt_rotor/build/tilt_rotor -- -j4 -l4
Invoked command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/tilt_rotor/build/tilt_rotor -- -j4 -l4
Invoking command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/tilt_rotor/build/tilt_rotor
Invoked command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/tilt_rotor/build/tilt_rotor
