[0.059s] Invoking command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/code/tilt_rotor -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/tilt_rotor/install/tilt_rotor
[0.146s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[1.655s] -- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
[1.907s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[1.944s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[2.010s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[2.118s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[2.275s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[2.696s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[2.718s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[4.114s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[4.664s] -- Found geometry_msgs: 4.8.0 (/opt/ros/humble/share/geometry_msgs/cmake)
[4.750s] -- Found sensor_msgs: 4.8.0 (/opt/ros/humble/share/sensor_msgs/cmake)
[4.882s] -- Building tilt_rotor package (Gazebo plugin building is optional)
[4.896s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[5.715s] -- Added test 'copyright' to check source files copyright and LICENSE
[5.780s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[5.789s] -- Configured cppcheck include dirs: /home/<USER>/code/tilt_rotor/plugins/tilt_rotor_plugin/include
[5.789s] -- Configured cppcheck exclude dirs and/or files: 
[5.864s] -- Added test 'cpplint' to check C / C++ code against the Google style
[5.864s] -- Configured cpplint exclude dirs and/or files: 
[5.887s] -- Added test 'flake8' to check Python code syntax and style conventions
[5.914s] -- Added test 'lint_cmake' to check CMake code style
[5.927s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[5.976s] -- Added test 'uncrustify' to check C / C++ code style
[5.976s] -- Configured uncrustify additional arguments: 
[5.999s] -- Added test 'xmllint' to check XML markup files
[6.024s] -- Configuring done
[6.109s] -- Generating done
[6.134s] -- Build files have been written to: /home/<USER>/code/tilt_rotor/build/tilt_rotor
[6.217s] Invoked command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/code/tilt_rotor -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/tilt_rotor/install/tilt_rotor
[6.231s] Invoking command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/tilt_rotor/build/tilt_rotor -- -j4 -l4
[6.565s] Invoked command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/tilt_rotor/build/tilt_rotor -- -j4 -l4
[6.657s] Invoking command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/tilt_rotor/build/tilt_rotor
[6.720s] -- Install configuration: "Release"
[6.723s] -- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/models
[6.724s] -- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/models/tilt_rotor_quadcopter
[6.731s] -- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/models/tilt_rotor_quadcopter/model.config
[6.731s] -- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/models/tilt_rotor_quadcopter/model.sdf
[6.732s] -- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/launch
[6.732s] -- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/launch/spawn_tilt_rotor.launch.py
[6.732s] -- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/config
[6.733s] -- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/config/tilt_rotor_params.yaml
[6.735s] -- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/lib/tilt_rotor/simple_controller.py
[6.743s] -- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/lib/tilt_rotor/test_commands.py
[6.744s] -- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/ament_index/resource_index/package_run_dependencies/tilt_rotor
[6.745s] -- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/ament_index/resource_index/parent_prefix_path/tilt_rotor
[6.747s] -- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/environment/ament_prefix_path.sh
[6.748s] -- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/environment/ament_prefix_path.dsv
[6.755s] -- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/environment/path.sh
[6.757s] -- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/environment/path.dsv
[6.761s] -- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/local_setup.bash
[6.769s] -- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/local_setup.sh
[6.769s] -- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/local_setup.zsh
[6.770s] -- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/local_setup.dsv
[6.770s] -- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.dsv
[6.770s] -- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/ament_index/resource_index/packages/tilt_rotor
[6.770s] -- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/cmake/tilt_rotorConfig.cmake
[6.771s] -- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/cmake/tilt_rotorConfig-version.cmake
[6.771s] -- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.xml
[6.795s] Invoked command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/tilt_rotor/build/tilt_rotor
