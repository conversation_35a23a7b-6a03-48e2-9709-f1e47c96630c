-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found geometry_msgs: 4.8.0 (/opt/ros/humble/share/geometry_msgs/cmake)
-- Found sensor_msgs: 4.8.0 (/opt/ros/humble/share/sensor_msgs/cmake)
-- Building tilt_rotor package (Gazebo plugin building is optional)
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'copyright' to check source files copyright and LICENSE
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: /home/<USER>/code/tilt_rotor/plugins/tilt_rotor_plugin/include
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'cpplint' to check C / C++ code against the Google style
-- Configured cpplint exclude dirs and/or files: 
-- Added test 'flake8' to check Python code syntax and style conventions
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'uncrustify' to check C / C++ code style
-- Configured uncrustify additional arguments: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/code/tilt_rotor/build/tilt_rotor
-- Install configuration: "Release"
-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/models
-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/models/tilt_rotor_quadcopter
-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/models/tilt_rotor_quadcopter/model.config
-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/models/tilt_rotor_quadcopter/model.sdf
-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/launch
-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/launch/spawn_tilt_rotor.launch.py
-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/config
-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/config/tilt_rotor_params.yaml
-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/lib/tilt_rotor/simple_controller.py
-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/lib/tilt_rotor/test_commands.py
-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/ament_index/resource_index/package_run_dependencies/tilt_rotor
-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/ament_index/resource_index/parent_prefix_path/tilt_rotor
-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/environment/ament_prefix_path.dsv
-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/environment/path.sh
-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/environment/path.dsv
-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/local_setup.bash
-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/local_setup.sh
-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/local_setup.zsh
-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/local_setup.dsv
-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.dsv
-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/ament_index/resource_index/packages/tilt_rotor
-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/cmake/tilt_rotorConfig.cmake
-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/cmake/tilt_rotorConfig-version.cmake
-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.xml
