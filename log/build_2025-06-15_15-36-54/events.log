[0.000000] (-) TimerEvent: {}
[0.001004] (tilt_rotor) JobQueued: {'identifier': 'tilt_rotor', 'dependencies': OrderedDict()}
[0.001202] (tilt_rotor) JobStarted: {'identifier': 'tilt_rotor'}
[0.049401] (tilt_rotor) JobProgress: {'identifier': 'tilt_rotor', 'progress': 'cmake'}
[0.052015] (tilt_rotor) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/code/tilt_rotor', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/code/tilt_rotor/install/tilt_rotor'], 'cwd': '/home/<USER>/code/tilt_rotor/build/tilt_rotor', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('USER', 'mahmood'), ('LC_TIME', 'lzh_TW'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>/code'), ('TERM_PROGRAM_VERSION', '1.100.2'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'lzh_TW'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('SYSTEMD_EXEC_PID', '4747'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mahmood'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'mahmood'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/home/<USER>/miniconda3/condabin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/mahmood-MacBookAir:@/tmp/.ICE-unix/1782,unix/mahmood-MacBookAir:/tmp/.ICE-unix/1782'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'lzh_TW'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/624a0ccb_369a_4914_9434_201d56e436b5'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'lzh_TW'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.1CKM72'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-4cc291407c.sock'), ('GNOME_TERMINAL_SERVICE', ':1.119'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniconda3/bin/python'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'lzh_TW'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'lzh_TW'), ('LC_IDENTIFICATION', 'lzh_TW'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/code/tilt_rotor/build/tilt_rotor'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CONDA_EXE', '/home/<USER>/miniconda3/bin/conda'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'lzh_TW'), ('LC_PAPER', 'lzh_TW'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.099121] (-) TimerEvent: {}
[0.146571] (tilt_rotor) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.199217] (-) TimerEvent: {}
[0.307510] (-) TimerEvent: {}
[0.414103] (-) TimerEvent: {}
[0.514553] (-) TimerEvent: {}
[0.614968] (-) TimerEvent: {}
[0.721545] (-) TimerEvent: {}
[0.822588] (-) TimerEvent: {}
[0.929245] (-) TimerEvent: {}
[1.030139] (-) TimerEvent: {}
[1.140933] (-) TimerEvent: {}
[1.248287] (-) TimerEvent: {}
[1.348972] (-) TimerEvent: {}
[1.457370] (-) TimerEvent: {}
[1.558466] (-) TimerEvent: {}
[1.655816] (tilt_rotor) StdoutLine: {'line': b'-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[1.663037] (-) TimerEvent: {}
[1.763677] (-) TimerEvent: {}
[1.864303] (-) TimerEvent: {}
[1.908264] (tilt_rotor) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[1.944519] (tilt_rotor) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[1.969074] (-) TimerEvent: {}
[2.010504] (tilt_rotor) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[2.070557] (-) TimerEvent: {}
[2.119079] (tilt_rotor) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[2.177179] (-) TimerEvent: {}
[2.276263] (tilt_rotor) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[2.277905] (-) TimerEvent: {}
[2.385478] (-) TimerEvent: {}
[2.487449] (-) TimerEvent: {}
[2.596235] (-) TimerEvent: {}
[2.696859] (tilt_rotor) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[2.708263] (-) TimerEvent: {}
[2.718722] (tilt_rotor) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[2.813906] (-) TimerEvent: {}
[2.917658] (-) TimerEvent: {}
[3.024764] (-) TimerEvent: {}
[3.125679] (-) TimerEvent: {}
[3.234415] (-) TimerEvent: {}
[3.343238] (-) TimerEvent: {}
[3.444002] (-) TimerEvent: {}
[3.551529] (-) TimerEvent: {}
[3.658879] (-) TimerEvent: {}
[3.759481] (-) TimerEvent: {}
[3.866888] (-) TimerEvent: {}
[3.967825] (-) TimerEvent: {}
[4.075302] (-) TimerEvent: {}
[4.115216] (tilt_rotor) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[4.177325] (-) TimerEvent: {}
[4.283176] (-) TimerEvent: {}
[4.386135] (-) TimerEvent: {}
[4.492458] (-) TimerEvent: {}
[4.593435] (-) TimerEvent: {}
[4.664738] (tilt_rotor) StdoutLine: {'line': b'-- Found geometry_msgs: 4.8.0 (/opt/ros/humble/share/geometry_msgs/cmake)\n'}
[4.699340] (-) TimerEvent: {}
[4.750542] (tilt_rotor) StdoutLine: {'line': b'-- Found sensor_msgs: 4.8.0 (/opt/ros/humble/share/sensor_msgs/cmake)\n'}
[4.799462] (-) TimerEvent: {}
[4.883068] (tilt_rotor) StdoutLine: {'line': b'-- Building tilt_rotor package (Gazebo plugin building is optional)\n'}
[4.897126] (tilt_rotor) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[4.899591] (-) TimerEvent: {}
[5.005493] (-) TimerEvent: {}
[5.107252] (-) TimerEvent: {}
[5.213653] (-) TimerEvent: {}
[5.314480] (-) TimerEvent: {}
[5.415081] (-) TimerEvent: {}
[5.519885] (-) TimerEvent: {}
[5.620811] (-) TimerEvent: {}
[5.715942] (tilt_rotor) StdoutLine: {'line': b"-- Added test 'copyright' to check source files copyright and LICENSE\n"}
[5.720896] (-) TimerEvent: {}
[5.780490] (tilt_rotor) StdoutLine: {'line': b"-- Added test 'cppcheck' to perform static code analysis on C / C++ code\n"}
[5.789477] (tilt_rotor) StdoutLine: {'line': b'-- Configured cppcheck include dirs: /home/<USER>/code/tilt_rotor/plugins/tilt_rotor_plugin/include\n'}
[5.789904] (tilt_rotor) StdoutLine: {'line': b'-- Configured cppcheck exclude dirs and/or files: \n'}
[5.828242] (-) TimerEvent: {}
[5.864806] (tilt_rotor) StdoutLine: {'line': b"-- Added test 'cpplint' to check C / C++ code against the Google style\n"}
[5.865258] (tilt_rotor) StdoutLine: {'line': b'-- Configured cpplint exclude dirs and/or files: \n'}
[5.888341] (tilt_rotor) StdoutLine: {'line': b"-- Added test 'flake8' to check Python code syntax and style conventions\n"}
[5.913704] (tilt_rotor) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[5.928171] (tilt_rotor) StdoutLine: {'line': b"-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257\n"}
[5.928606] (-) TimerEvent: {}
[5.976828] (tilt_rotor) StdoutLine: {'line': b"-- Added test 'uncrustify' to check C / C++ code style\n"}
[5.977354] (tilt_rotor) StdoutLine: {'line': b'-- Configured uncrustify additional arguments: \n'}
[6.000036] (tilt_rotor) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[6.024989] (tilt_rotor) StdoutLine: {'line': b'-- Configuring done\n'}
[6.034136] (-) TimerEvent: {}
[6.108968] (tilt_rotor) StdoutLine: {'line': b'-- Generating done\n'}
[6.134190] (tilt_rotor) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/code/tilt_rotor/build/tilt_rotor\n'}
[6.135646] (-) TimerEvent: {}
[6.208603] (tilt_rotor) CommandEnded: {'returncode': 0}
[6.230231] (tilt_rotor) JobProgress: {'identifier': 'tilt_rotor', 'progress': 'build'}
[6.230371] (tilt_rotor) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/code/tilt_rotor/build/tilt_rotor', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/code/tilt_rotor/build/tilt_rotor', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('USER', 'mahmood'), ('LC_TIME', 'lzh_TW'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>/code'), ('TERM_PROGRAM_VERSION', '1.100.2'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'lzh_TW'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('SYSTEMD_EXEC_PID', '4747'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mahmood'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'mahmood'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/home/<USER>/miniconda3/condabin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/mahmood-MacBookAir:@/tmp/.ICE-unix/1782,unix/mahmood-MacBookAir:/tmp/.ICE-unix/1782'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'lzh_TW'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/624a0ccb_369a_4914_9434_201d56e436b5'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'lzh_TW'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.1CKM72'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-4cc291407c.sock'), ('GNOME_TERMINAL_SERVICE', ':1.119'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniconda3/bin/python'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'lzh_TW'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'lzh_TW'), ('LC_IDENTIFICATION', 'lzh_TW'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/code/tilt_rotor/build/tilt_rotor'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CONDA_EXE', '/home/<USER>/miniconda3/bin/conda'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'lzh_TW'), ('LC_PAPER', 'lzh_TW'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[6.242420] (-) TimerEvent: {}
[6.345032] (-) TimerEvent: {}
[6.450660] (-) TimerEvent: {}
[6.551320] (-) TimerEvent: {}
[6.565574] (tilt_rotor) CommandEnded: {'returncode': 0}
[6.577831] (tilt_rotor) JobProgress: {'identifier': 'tilt_rotor', 'progress': 'install'}
[6.639947] (tilt_rotor) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/code/tilt_rotor/build/tilt_rotor'], 'cwd': '/home/<USER>/code/tilt_rotor/build/tilt_rotor', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('USER', 'mahmood'), ('LC_TIME', 'lzh_TW'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>/code'), ('TERM_PROGRAM_VERSION', '1.100.2'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'lzh_TW'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('SYSTEMD_EXEC_PID', '4747'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mahmood'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'mahmood'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/home/<USER>/miniconda3/condabin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/mahmood-MacBookAir:@/tmp/.ICE-unix/1782,unix/mahmood-MacBookAir:/tmp/.ICE-unix/1782'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'lzh_TW'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/624a0ccb_369a_4914_9434_201d56e436b5'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'lzh_TW'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.1CKM72'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-4cc291407c.sock'), ('GNOME_TERMINAL_SERVICE', ':1.119'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniconda3/bin/python'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'lzh_TW'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'lzh_TW'), ('LC_IDENTIFICATION', 'lzh_TW'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/code/tilt_rotor/build/tilt_rotor'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CONDA_EXE', '/home/<USER>/miniconda3/bin/conda'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'lzh_TW'), ('LC_PAPER', 'lzh_TW'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[6.659201] (-) TimerEvent: {}
[6.720519] (tilt_rotor) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[6.722146] (tilt_rotor) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/models\n'}
[6.724128] (tilt_rotor) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/models/tilt_rotor_quadcopter\n'}
[6.725625] (tilt_rotor) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/models/tilt_rotor_quadcopter/model.config\n'}
[6.732526] (tilt_rotor) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/models/tilt_rotor_quadcopter/model.sdf\n'}
[6.732777] (tilt_rotor) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/launch\n'}
[6.733078] (tilt_rotor) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/launch/spawn_tilt_rotor.launch.py\n'}
[6.733400] (tilt_rotor) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/config\n'}
[6.733620] (tilt_rotor) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/config/tilt_rotor_params.yaml\n'}
[6.734438] (tilt_rotor) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/lib/tilt_rotor/simple_controller.py\n'}
[6.744230] (tilt_rotor) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/lib/tilt_rotor/test_commands.py\n'}
[6.744814] (tilt_rotor) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/ament_index/resource_index/package_run_dependencies/tilt_rotor\n'}
[6.745129] (tilt_rotor) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/ament_index/resource_index/parent_prefix_path/tilt_rotor\n'}
[6.747639] (tilt_rotor) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/environment/ament_prefix_path.sh\n'}
[6.748624] (tilt_rotor) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/environment/ament_prefix_path.dsv\n'}
[6.749292] (tilt_rotor) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/environment/path.sh\n'}
[6.756627] (tilt_rotor) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/environment/path.dsv\n'}
[6.758533] (tilt_rotor) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/local_setup.bash\n'}
[6.768627] (-) TimerEvent: {}
[6.769696] (tilt_rotor) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/local_setup.sh\n'}
[6.770232] (tilt_rotor) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/local_setup.zsh\n'}
[6.770625] (tilt_rotor) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/local_setup.dsv\n'}
[6.770905] (tilt_rotor) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.dsv\n'}
[6.771142] (tilt_rotor) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/ament_index/resource_index/packages/tilt_rotor\n'}
[6.771413] (tilt_rotor) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/cmake/tilt_rotorConfig.cmake\n'}
[6.771628] (tilt_rotor) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/cmake/tilt_rotorConfig-version.cmake\n'}
[6.771834] (tilt_rotor) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.xml\n'}
[6.794625] (tilt_rotor) CommandEnded: {'returncode': 0}
[6.869719] (-) TimerEvent: {}
[6.977600] (-) TimerEvent: {}
[7.030317] (tilt_rotor) JobEnded: {'identifier': 'tilt_rotor', 'rc': 0}
[7.042491] (-) EventReactorShutdown: {}
