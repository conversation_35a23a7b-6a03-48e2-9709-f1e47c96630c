[0.833s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'tilt_rotor']
[0.839s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=4, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['tilt_rotor'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x72d11fe6a0e0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x72d11fe69c30>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x72d11fe69c30>>, mixin_verb=('build',))
[2.162s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[2.162s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[2.163s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[2.163s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[2.163s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[2.163s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[2.163s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/code/tilt_rotor'
[2.164s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[2.165s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[2.166s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[2.167s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[2.167s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[2.167s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[2.167s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[2.167s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[2.167s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[2.284s] DEBUG:colcon.colcon_core.package_identification:Package '.' with type 'ros.ament_cmake' and name 'tilt_rotor'
[2.284s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[2.284s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[2.284s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[2.284s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[2.285s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[2.460s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[2.460s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[2.471s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 292 installed packages in /opt/ros/humble
[2.481s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[2.827s] Level 5:colcon.colcon_core.verb:set package 'tilt_rotor' build argument 'cmake_args' from command line to 'None'
[2.828s] Level 5:colcon.colcon_core.verb:set package 'tilt_rotor' build argument 'cmake_target' from command line to 'None'
[2.828s] Level 5:colcon.colcon_core.verb:set package 'tilt_rotor' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[2.828s] Level 5:colcon.colcon_core.verb:set package 'tilt_rotor' build argument 'cmake_clean_cache' from command line to 'False'
[2.828s] Level 5:colcon.colcon_core.verb:set package 'tilt_rotor' build argument 'cmake_clean_first' from command line to 'False'
[2.828s] Level 5:colcon.colcon_core.verb:set package 'tilt_rotor' build argument 'cmake_force_configure' from command line to 'False'
[2.828s] Level 5:colcon.colcon_core.verb:set package 'tilt_rotor' build argument 'ament_cmake_args' from command line to 'None'
[2.828s] Level 5:colcon.colcon_core.verb:set package 'tilt_rotor' build argument 'catkin_cmake_args' from command line to 'None'
[2.828s] Level 5:colcon.colcon_core.verb:set package 'tilt_rotor' build argument 'catkin_skip_building_tests' from command line to 'False'
[2.828s] DEBUG:colcon.colcon_core.verb:Building package 'tilt_rotor' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/code/tilt_rotor/build/tilt_rotor', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/code/tilt_rotor/install/tilt_rotor', 'merge_install': False, 'path': '/home/<USER>/code/tilt_rotor', 'symlink_install': False, 'test_result_base': None}
[2.835s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[2.837s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[2.837s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/code/tilt_rotor' with build type 'ament_cmake'
[2.837s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/code/tilt_rotor'
[2.850s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[2.850s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[2.850s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[2.898s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/code/tilt_rotor -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/tilt_rotor/install/tilt_rotor
[9.061s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/code/tilt_rotor -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/tilt_rotor/install/tilt_rotor
[9.070s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/tilt_rotor/build/tilt_rotor -- -j4 -l4
[9.415s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/tilt_rotor/build/tilt_rotor -- -j4 -l4
[9.496s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/tilt_rotor/build/tilt_rotor
[9.619s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(tilt_rotor)
[9.633s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/tilt_rotor/build/tilt_rotor' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/tilt_rotor/build/tilt_rotor
[9.658s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor' for CMake module files
[9.668s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor' for CMake config files
[9.670s] Level 1:colcon.colcon_core.shell:create_environment_hook('tilt_rotor', 'cmake_prefix_path')
[9.672s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/hook/cmake_prefix_path.ps1'
[9.691s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/hook/cmake_prefix_path.dsv'
[9.704s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/hook/cmake_prefix_path.sh'
[9.717s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/lib'
[9.718s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/bin'
[9.718s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/lib/pkgconfig/tilt_rotor.pc'
[9.719s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/lib/python3.10/site-packages'
[9.720s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/bin'
[9.722s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.ps1'
[9.733s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.dsv'
[9.743s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.sh'
[9.756s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.bash'
[9.765s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.zsh'
[9.769s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/colcon-core/packages/tilt_rotor)
[9.779s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(tilt_rotor)
[9.781s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor' for CMake module files
[9.791s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor' for CMake config files
[9.793s] Level 1:colcon.colcon_core.shell:create_environment_hook('tilt_rotor', 'cmake_prefix_path')
[9.795s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/hook/cmake_prefix_path.ps1'
[9.804s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/hook/cmake_prefix_path.dsv'
[9.805s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/hook/cmake_prefix_path.sh'
[9.814s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/lib'
[9.818s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/bin'
[9.818s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/lib/pkgconfig/tilt_rotor.pc'
[9.827s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/lib/python3.10/site-packages'
[9.829s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/tilt_rotor/install/tilt_rotor/bin'
[9.831s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.ps1'
[9.842s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.dsv'
[9.853s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.sh'
[9.856s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.bash'
[9.864s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.zsh'
[9.866s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/colcon-core/packages/tilt_rotor)
[9.867s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[9.869s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[9.878s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[9.878s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[9.952s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[9.953s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[9.953s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[10.073s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[10.074s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/tilt_rotor/install/local_setup.ps1'
[10.084s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/code/tilt_rotor/install/_local_setup_util_ps1.py'
[10.096s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/tilt_rotor/install/setup.ps1'
[10.101s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/tilt_rotor/install/local_setup.sh'
[10.109s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/code/tilt_rotor/install/_local_setup_util_sh.py'
[10.120s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/tilt_rotor/install/setup.sh'
[10.124s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/tilt_rotor/install/local_setup.bash'
[10.133s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/tilt_rotor/install/setup.bash'
[10.145s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/tilt_rotor/install/local_setup.zsh'
[10.150s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/tilt_rotor/install/setup.zsh'
