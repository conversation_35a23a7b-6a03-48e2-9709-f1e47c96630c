#!/usr/bin/env python3

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription
from launch.conditions import IfCondition
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from ament_index_python.packages import get_package_share_directory

def generate_launch_description():
    # Package directories
    pkg_tilt_rotor = get_package_share_directory('tilt_rotor')
    pkg_ros_gz_sim = get_package_share_directory('ros_gz_sim')
    
    # Launch arguments
    world_arg = DeclareLaunchArgument(
        'world',
        default_value='empty.sdf',
        description='World file to load'
    )
    
    model_name_arg = DeclareLaunchArgument(
        'model_name',
        default_value='tilt_rotor_quadcopter',
        description='Name of the model to spawn'
    )
    
    x_arg = DeclareLaunchArgument(
        'x',
        default_value='0.0',
        description='X position to spawn the model'
    )
    
    y_arg = DeclareLaunchArgument(
        'y',
        default_value='0.0',
        description='Y position to spawn the model'
    )
    
    z_arg = DeclareLaunchArgument(
        'z',
        default_value='1.0',
        description='Z position to spawn the model'
    )
    
    use_sim_time_arg = DeclareLaunchArgument(
        'use_sim_time',
        default_value='true',
        description='Use simulation time'
    )
    
    # Gazebo launch
    gazebo = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            PathJoinSubstitution([
                FindPackageShare('ros_gz_sim'),
                'launch',
                'gz_sim.launch.py'
            ])
        ]),
        launch_arguments={
            'gz_args': ['-r -v 4 ', LaunchConfiguration('world')]
        }.items()
    )
    
    # Model path
    model_path = os.path.join(pkg_tilt_rotor, 'models', 'tilt_rotor_quadcopter', 'model.sdf')
    
    # Spawn model
    spawn_model = Node(
        package='ros_gz_sim',
        executable='create',
        arguments=[
            '-file', model_path,
            '-name', LaunchConfiguration('model_name'),
            '-x', LaunchConfiguration('x'),
            '-y', LaunchConfiguration('y'),
            '-z', LaunchConfiguration('z')
        ],
        output='screen'
    )
    
    # Bridge for ROS-Gazebo communication
    bridge = Node(
        package='ros_gz_bridge',
        executable='parameter_bridge',
        arguments=[
            '/tilt_rotor_quadcopter/imu/data@sensor_msgs/msg/<EMAIL>',
            '/tilt_rotor_quadcopter/joint_states@sensor_msgs/msg/<EMAIL>',
            '/tilt_rotor_quadcopter/pose@geometry_msgs/msg/<EMAIL>.Pose_V',
            '/clock@rosgraph_msgs/msg/Clock[ignition.msgs.Clock'
        ],
        output='screen'
    )
    
    # Robot state publisher (for joint states)
    robot_state_publisher = Node(
        package='robot_state_publisher',
        executable='robot_state_publisher',
        name='robot_state_publisher',
        output='screen',
        parameters=[{
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'robot_description': ''  # We don't have URDF, using SDF directly
        }]
    )
    
    # Simple controller node for testing
    controller_node = Node(
        package='tilt_rotor',
        executable='simple_controller.py',
        name='simple_controller',
        output='screen',
        parameters=[{
            'use_sim_time': LaunchConfiguration('use_sim_time')
        }]
    )
    
    return LaunchDescription([
        world_arg,
        model_name_arg,
        x_arg,
        y_arg,
        z_arg,
        use_sim_time_arg,
        gazebo,
        spawn_model,
        bridge,
        robot_state_publisher,
        # controller_node,  # Uncomment when controller is implemented
    ])
