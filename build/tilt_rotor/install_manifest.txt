/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/models/tilt_rotor_quadcopter/model.config
/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/models/tilt_rotor_quadcopter/model.sdf
/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/launch/spawn_tilt_rotor.launch.py
/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/config/tilt_rotor_params.yaml
/home/<USER>/code/tilt_rotor/install/tilt_rotor/lib/tilt_rotor/simple_controller.py
/home/<USER>/code/tilt_rotor/install/tilt_rotor/lib/tilt_rotor/test_commands.py
/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/ament_index/resource_index/package_run_dependencies/tilt_rotor
/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/ament_index/resource_index/parent_prefix_path/tilt_rotor
/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/environment/ament_prefix_path.sh
/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/environment/ament_prefix_path.dsv
/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/environment/path.sh
/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/environment/path.dsv
/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/local_setup.bash
/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/local_setup.sh
/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/local_setup.zsh
/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/local_setup.dsv
/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.dsv
/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/ament_index/resource_index/packages/tilt_rotor
/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/cmake/tilt_rotorConfig.cmake
/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/cmake/tilt_rotorConfig-version.cmake
/home/<USER>/code/tilt_rotor/install/tilt_rotor/share/tilt_rotor/package.xml