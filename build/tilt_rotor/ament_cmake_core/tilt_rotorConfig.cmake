# generated from ament/cmake/core/templates/nameConfig.cmake.in

# prevent multiple inclusion
if(_tilt_rotor_CONFIG_INCLUDED)
  # ensure to keep the found flag the same
  if(NOT DEFINED tilt_rotor_FOUND)
    # explicitly set it to FALSE, otherwise CMake will set it to TRUE
    set(tilt_rotor_FOUND FALSE)
  elseif(NOT tilt_rotor_FOUND)
    # use separate condition to avoid uninitialized variable warning
    set(tilt_rotor_FOUND FALSE)
  endif()
  return()
endif()
set(_tilt_rotor_CONFIG_INCLUDED TRUE)

# output package information
if(NOT tilt_rotor_FIND_QUIETLY)
  message(STATUS "Found tilt_rotor: 0.0.0 (${tilt_rotor_DIR})")
endif()

# warn when using a deprecated package
if(NOT "" STREQUAL "")
  set(_msg "Package 'tilt_rotor' is deprecated")
  # append custom deprecation text if available
  if(NOT "" STREQUAL "TRUE")
    set(_msg "${_msg} ()")
  endif()
  # optionally quiet the deprecation message
  if(NOT ${tilt_rotor_DEPRECATED_QUIET})
    message(DEPRECATION "${_msg}")
  endif()
endif()

# flag package as ament-based to distinguish it after being find_package()-ed
set(tilt_rotor_FOUND_AMENT_PACKAGE TRUE)

# include all config extra files
set(_extras "")
foreach(_extra ${_extras})
  include("${tilt_rotor_DIR}/${_extra}")
endforeach()
