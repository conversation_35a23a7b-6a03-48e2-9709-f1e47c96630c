set(_AMENT_PACKAGE_NAME "tilt_rotor")
set(tilt_rotor_VERSION "0.0.0")
set(tilt_rotor_MAINTAINER "Your Name <<EMAIL>>")
set(tilt_rotor_BUILD_DEPENDS "rclcpp" "std_msgs" "geometry_msgs" "sensor_msgs" "ros_gz_sim" "ros_gz_bridge")
set(tilt_rotor_BUILDTOOL_DEPENDS "ament_cmake")
set(tilt_rotor_BUILD_EXPORT_DEPENDS "rclcpp" "std_msgs" "geometry_msgs" "sensor_msgs" "ros_gz_sim" "ros_gz_bridge")
set(tilt_rotor_BUILDTOOL_EXPORT_DEPENDS )
set(tilt_rotor_EXEC_DEPENDS "rclcpp" "std_msgs" "geometry_msgs" "sensor_msgs" "ros_gz_sim" "ros_gz_bridge")
set(tilt_rotor_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(tilt_rotor_GROUP_DEPENDS )
set(tilt_rotor_MEMBER_OF_GROUPS )
set(tilt_rotor_DEPRECATED "")
set(tilt_rotor_EXPORT_TAGS)
list(APPEND tilt_rotor_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
