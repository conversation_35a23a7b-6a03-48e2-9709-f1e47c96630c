#!/bin/bash

# Build and Test Script for Tilt Rotor Quadcopter
# This script builds the project and provides testing options

set -e  # Exit on any error

echo "=== Tilt Rotor Quadcopter Build and Test Script ==="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in a ROS2 workspace
if [ ! -f "package.xml" ]; then
    print_error "package.xml not found. Please run this script from the tilt_rotor package directory."
    exit 1
fi

# Check if ROS2 is sourced
if [ -z "$ROS_DISTRO" ]; then
    print_error "ROS2 not sourced. Please source your ROS2 installation first:"
    echo "  source /opt/ros/humble/setup.bash  # or your ROS2 distro"
    exit 1
fi

print_status "ROS2 distro: $ROS_DISTRO"

# Navigate to workspace root (assuming we're in src/tilt_rotor)
if [ -d "../../" ] && [ -f "../../src/tilt_rotor/package.xml" ]; then
    cd ../../
    print_status "Changed to workspace root: $(pwd)"
elif [ -d "../" ] && [ -f "../package.xml" ]; then
    cd ../
    print_status "Changed to workspace root: $(pwd)"
else
    print_warning "Could not determine workspace root. Assuming current directory is workspace root."
fi

# Build the package
print_status "Building tilt_rotor package..."
if colcon build --packages-select tilt_rotor --cmake-args -DCMAKE_BUILD_TYPE=Release; then
    print_status "Build successful!"
else
    print_error "Build failed!"
    exit 1
fi

# Source the workspace
print_status "Sourcing workspace..."
source install/setup.bash

# Check if Gazebo Ionic is available
if ! command -v gz &> /dev/null; then
    print_warning "Gazebo Ionic (gz command) not found. Please install Gazebo Ionic."
    print_warning "You can still build the package, but simulation won't work."
fi

# Function to show usage
show_usage() {
    echo ""
    echo "Build completed successfully!"
    echo ""
    echo "Usage options:"
    echo "  $0 --test-build    : Test that the package builds correctly (default)"
    echo "  $0 --launch        : Launch the simulation"
    echo "  $0 --test-commands : Run command tests"
    echo "  $0 --demo          : Run demo controller"
    echo "  $0 --help          : Show this help"
    echo ""
    echo "Manual commands:"
    echo "  # Launch simulation:"
    echo "  ros2 launch tilt_rotor spawn_tilt_rotor.launch.py"
    echo ""
    echo "  # Run demo controller:"
    echo "  ros2 run tilt_rotor simple_controller.py"
    echo ""
    echo "  # Test commands:"
    echo "  ros2 run tilt_rotor test_commands.py"
    echo ""
}

# Parse command line arguments
case "${1:-}" in
    --launch)
        print_status "Launching simulation..."
        ros2 launch tilt_rotor spawn_tilt_rotor.launch.py
        ;;
    --test-commands)
        print_status "Running command tests..."
        print_warning "Make sure the simulation is running in another terminal first!"
        sleep 2
        ros2 run tilt_rotor test_commands.py
        ;;
    --demo)
        print_status "Running demo controller..."
        print_warning "Make sure the simulation is running in another terminal first!"
        sleep 2
        ros2 run tilt_rotor simple_controller.py
        ;;
    --help)
        show_usage
        ;;
    --test-build|"")
        print_status "Build test completed successfully!"
        show_usage
        ;;
    *)
        print_error "Unknown option: $1"
        show_usage
        exit 1
        ;;
esac

print_status "Script completed!"
