#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from std_msgs.msg import Float64MultiArray
from geometry_msgs.msg import Twist
from sensor_msgs.msg import Imu, JointState
import math
import time

class SimpleTiltRotorController(Node):
    """
    Simple controller for testing the tilt rotor quadcopter.
    Provides basic hover and movement capabilities.
    """
    
    def __init__(self):
        super().__init__('simple_tilt_rotor_controller')
        
        # Publishers
        self.tilt_cmd_pub = self.create_publisher(
            Float64MultiArray, 
            '/tilt_rotor_quadcopter/tilt_command', 
            10
        )
        
        self.motor_cmd_pub = self.create_publisher(
            Float64MultiArray, 
            '/tilt_rotor_quadcopter/motor_command', 
            10
        )
        
        # Subscribers
        self.cmd_vel_sub = self.create_subscription(
            Twist,
            '/tilt_rotor_quadcopter/cmd_vel',
            self.cmd_vel_callback,
            10
        )
        
        self.imu_sub = self.create_subscription(
            Imu,
            '/tilt_rotor_quadcopter/imu/data',
            self.imu_callback,
            10
        )
        
        self.joint_state_sub = self.create_subscription(
            JointState,
            '/tilt_rotor_quadcopter/joint_states',
            self.joint_state_callback,
            10
        )
        
        # Control timer
        self.control_timer = self.create_timer(0.02, self.control_loop)  # 50 Hz
        
        # State variables
        self.current_cmd_vel = Twist()
        self.current_imu = Imu()
        self.current_joint_states = JointState()
        
        # Control parameters
        self.hover_thrust = 200.0  # Base thrust for hovering
        self.max_tilt_angle = math.pi / 3  # 60 degrees max tilt
        
        self.get_logger().info('Simple Tilt Rotor Controller started')
        
        # Demo mode
        self.demo_mode = True
        self.demo_start_time = time.time()
        
    def cmd_vel_callback(self, msg):
        """Handle velocity commands"""
        self.current_cmd_vel = msg
        self.demo_mode = False  # Disable demo when receiving commands
        
    def imu_callback(self, msg):
        """Handle IMU data"""
        self.current_imu = msg
        
    def joint_state_callback(self, msg):
        """Handle joint state data"""
        self.current_joint_states = msg
        
    def control_loop(self):
        """Main control loop"""
        if self.demo_mode:
            self.run_demo()
        else:
            self.process_cmd_vel()
            
    def run_demo(self):
        """Run a simple demo sequence"""
        elapsed_time = time.time() - self.demo_start_time
        
        # Create demo command
        demo_cmd = Twist()
        
        if elapsed_time < 5.0:
            # Hover for 5 seconds
            demo_cmd.linear.z = 0.5
            self.get_logger().info('Demo: Hovering', throttle_duration_sec=1.0)
            
        elif elapsed_time < 10.0:
            # Move forward
            demo_cmd.linear.x = 1.0
            demo_cmd.linear.z = 0.5
            self.get_logger().info('Demo: Moving forward', throttle_duration_sec=1.0)
            
        elif elapsed_time < 15.0:
            # Move backward
            demo_cmd.linear.x = -1.0
            demo_cmd.linear.z = 0.5
            self.get_logger().info('Demo: Moving backward', throttle_duration_sec=1.0)
            
        elif elapsed_time < 20.0:
            # Move left
            demo_cmd.linear.y = 1.0
            demo_cmd.linear.z = 0.5
            self.get_logger().info('Demo: Moving left', throttle_duration_sec=1.0)
            
        elif elapsed_time < 25.0:
            # Move right
            demo_cmd.linear.y = -1.0
            demo_cmd.linear.z = 0.5
            self.get_logger().info('Demo: Moving right', throttle_duration_sec=1.0)
            
        elif elapsed_time < 30.0:
            # Rotate
            demo_cmd.angular.z = 1.0
            demo_cmd.linear.z = 0.5
            self.get_logger().info('Demo: Rotating', throttle_duration_sec=1.0)
            
        else:
            # Reset demo
            self.demo_start_time = time.time()
            demo_cmd.linear.z = 0.5
            
        self.current_cmd_vel = demo_cmd
        self.process_cmd_vel()
        
    def process_cmd_vel(self):
        """Process velocity commands and generate motor/tilt commands"""
        
        # Extract command values
        linear_x = self.current_cmd_vel.linear.x
        linear_y = self.current_cmd_vel.linear.y
        linear_z = self.current_cmd_vel.linear.z
        angular_x = self.current_cmd_vel.angular.x
        angular_y = self.current_cmd_vel.angular.y
        angular_z = self.current_cmd_vel.angular.z
        
        # Calculate motor commands (simple mixing)
        base_thrust = self.hover_thrust + linear_z * 100.0
        
        roll_factor = angular_x * 50.0
        pitch_factor = angular_y * 50.0
        yaw_factor = angular_z * 50.0
        
        # Motor mixing for quadcopter (X configuration)
        motor_commands = [
            base_thrust + roll_factor + pitch_factor - yaw_factor,  # Front left
            base_thrust - roll_factor + pitch_factor + yaw_factor,  # Front right
            base_thrust + roll_factor - pitch_factor + yaw_factor,  # Rear left
            base_thrust - roll_factor - pitch_factor - yaw_factor   # Rear right
        ]
        
        # Clamp motor commands
        motor_commands = [max(0.0, min(1000.0, cmd)) for cmd in motor_commands]
        
        # Calculate tilt commands for translation
        tilt_magnitude = math.sqrt(linear_x**2 + linear_y**2) * 0.3
        tilt_magnitude = min(tilt_magnitude, self.max_tilt_angle)
        
        if abs(linear_x) > 0.1 or abs(linear_y) > 0.1:
            tilt_direction = math.atan2(linear_y, linear_x)
            
            # Apply tilt to all rotors in direction of movement
            tilt_commands = [
                tilt_magnitude * math.cos(tilt_direction - i * math.pi / 2.0)
                for i in range(4)
            ]
        else:
            # No horizontal movement, keep rotors vertical
            tilt_commands = [0.0, 0.0, 0.0, 0.0]
        
        # Clamp tilt commands
        tilt_commands = [max(-self.max_tilt_angle, min(self.max_tilt_angle, cmd)) 
                        for cmd in tilt_commands]
        
        # Publish commands
        motor_msg = Float64MultiArray()
        motor_msg.data = motor_commands
        self.motor_cmd_pub.publish(motor_msg)
        
        tilt_msg = Float64MultiArray()
        tilt_msg.data = tilt_commands
        self.tilt_cmd_pub.publish(tilt_msg)

def main(args=None):
    rclpy.init(args=args)
    
    controller = SimpleTiltRotorController()
    
    try:
        rclpy.spin(controller)
    except KeyboardInterrupt:
        pass
    finally:
        controller.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
