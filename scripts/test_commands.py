#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from std_msgs.msg import Float64MultiArray
from geometry_msgs.msg import Twist
import time
import math

class TiltRotorTester(Node):
    """
    Test script for the tilt rotor quadcopter.
    Sends various commands to test functionality.
    """
    
    def __init__(self):
        super().__init__('tilt_rotor_tester')
        
        # Publishers
        self.tilt_cmd_pub = self.create_publisher(
            Float64MultiArray, 
            '/tilt_rotor_quadcopter/tilt_command', 
            10
        )
        
        self.motor_cmd_pub = self.create_publisher(
            Float64MultiArray, 
            '/tilt_rotor_quadcopter/motor_command', 
            10
        )
        
        self.cmd_vel_pub = self.create_publisher(
            Twist,
            '/tilt_rotor_quadcopter/cmd_vel',
            10
        )
        
        self.get_logger().info('Tilt Rotor Tester started')
        
    def test_motor_commands(self):
        """Test motor commands"""
        self.get_logger().info('Testing motor commands...')
        
        # Gradual motor spin-up
        for speed in [0, 50, 100, 150, 200]:
            motor_msg = Float64MultiArray()
            motor_msg.data = [float(speed)] * 4
            self.motor_cmd_pub.publish(motor_msg)
            self.get_logger().info(f'Motor speed: {speed}')
            time.sleep(2.0)
            
        # Stop motors
        motor_msg = Float64MultiArray()
        motor_msg.data = [0.0] * 4
        self.motor_cmd_pub.publish(motor_msg)
        self.get_logger().info('Motors stopped')
        
    def test_tilt_commands(self):
        """Test tilt commands"""
        self.get_logger().info('Testing tilt commands...')
        
        # Test different tilt angles
        angles = [0.0, 0.5, 1.0, 0.5, 0.0, -0.5, -1.0, -0.5, 0.0]
        
        for angle in angles:
            tilt_msg = Float64MultiArray()
            tilt_msg.data = [angle] * 4
            self.tilt_cmd_pub.publish(tilt_msg)
            self.get_logger().info(f'Tilt angle: {angle:.2f} rad ({math.degrees(angle):.1f}°)')
            time.sleep(2.0)
            
    def test_individual_tilts(self):
        """Test individual rotor tilts"""
        self.get_logger().info('Testing individual tilt commands...')
        
        # Test each rotor individually
        for i in range(4):
            tilt_msg = Float64MultiArray()
            tilt_msg.data = [0.0] * 4
            tilt_msg.data[i] = 1.0  # Tilt one rotor
            self.tilt_cmd_pub.publish(tilt_msg)
            self.get_logger().info(f'Tilting rotor {i}')
            time.sleep(2.0)
            
        # Reset all tilts
        tilt_msg = Float64MultiArray()
        tilt_msg.data = [0.0] * 4
        self.tilt_cmd_pub.publish(tilt_msg)
        self.get_logger().info('All tilts reset')
        
    def test_twist_commands(self):
        """Test twist commands"""
        self.get_logger().info('Testing twist commands...')
        
        # Test hover
        twist = Twist()
        twist.linear.z = 0.5
        self.cmd_vel_pub.publish(twist)
        self.get_logger().info('Hover command')
        time.sleep(3.0)
        
        # Test forward
        twist = Twist()
        twist.linear.x = 1.0
        twist.linear.z = 0.5
        self.cmd_vel_pub.publish(twist)
        self.get_logger().info('Forward command')
        time.sleep(3.0)
        
        # Test rotation
        twist = Twist()
        twist.angular.z = 1.0
        twist.linear.z = 0.5
        self.cmd_vel_pub.publish(twist)
        self.get_logger().info('Rotation command')
        time.sleep(3.0)
        
        # Stop
        twist = Twist()
        self.cmd_vel_pub.publish(twist)
        self.get_logger().info('Stop command')
        
    def run_full_test(self):
        """Run complete test sequence"""
        self.get_logger().info('Starting full test sequence...')
        
        time.sleep(2.0)  # Wait for connections
        
        self.test_motor_commands()
        time.sleep(2.0)
        
        self.test_tilt_commands()
        time.sleep(2.0)
        
        self.test_individual_tilts()
        time.sleep(2.0)
        
        self.test_twist_commands()
        
        self.get_logger().info('Test sequence completed!')

def main(args=None):
    rclpy.init(args=args)
    
    tester = TiltRotorTester()
    
    try:
        tester.run_full_test()
        time.sleep(2.0)
    except KeyboardInterrupt:
        pass
    finally:
        tester.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
