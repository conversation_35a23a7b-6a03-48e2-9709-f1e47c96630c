<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>tilt_rotor</name>
  <version>0.0.0</version>
  <description>Tilt rotor quadcopter drone model for Gazebo Ionic with ROS2</description>
  <maintainer email="<EMAIL>">Your Name</maintainer>
  <license>Apache-2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <depend>rclcpp</depend>
  <depend>std_msgs</depend>
  <depend>geometry_msgs</depend>
  <depend>sensor_msgs</depend>

  <!-- Gazebo dependencies (optional) -->
  <depend condition="$ROS_DISTRO == humble">ros_gz_sim</depend>
  <depend condition="$ROS_DISTRO == humble">ros_gz_bridge</depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
